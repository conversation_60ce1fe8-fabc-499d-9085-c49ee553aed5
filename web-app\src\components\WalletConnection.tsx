import React from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { WalletMultiButton, WalletDisconnectButton } from './WalletConnectionProvider';

export const WalletConnection: React.FC = () => {
  const { connected, publicKey } = useWallet();

  return (
    <div className="flex items-center justify-between p-4 bg-gray-50 border-b">
      <div className="flex items-center space-x-4">
        <h1 className="text-xl font-bold text-gray-800">Solana Token Creator</h1>
        {connected && publicKey && (
          <div className="text-sm text-gray-600">
            Connected: {publicKey.toString().slice(0, 8)}...{publicKey.toString().slice(-8)}
          </div>
        )}
      </div>
      
      <div className="flex items-center space-x-2">
        <WalletMultiButton className="!bg-blue-600 hover:!bg-blue-700" />
        {connected && (
          <WalletDisconnectButton className="!bg-red-600 hover:!bg-red-700" />
        )}
      </div>
    </div>
  );
};
