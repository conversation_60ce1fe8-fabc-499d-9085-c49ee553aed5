import React, { useState } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { TokenCreationService } from '../services/TokenCreationService';
import type { TokenMetadata, TokenCreationParams, TokenCreationResult } from '../services/TokenCreationService';
import { TransactionStatus } from './TransactionStatus';

interface FormData {
  name: string;
  symbol: string;
  description: string;
  image: string;
  decimals: number;
  initialSupply: string;
  sellerFeeBasisPoints: number;
  revokeAuthorities: boolean;
  creatorName: string;
  creatorSite: string;
  externalUrl: string;
  twitter: string;
  telegram: string;
  discord: string;
}

const initialFormData: FormData = {
  name: '',
  symbol: '',
  description: '',
  image: '',
  decimals: 9,
  initialSupply: '1000000',
  sellerFeeBasisPoints: 250, // 2.5%
  revokeAuthorities: true,
  creatorName: '',
  creatorSite: '',
  externalUrl: '',
  twitter: '',
  telegram: '',
  discord: '',
};

export const TokenCreationForm: React.FC = () => {
  const { wallet, connected } = useWallet();
  const [formData, setFormData] = useState<FormData>(initialFormData);
  const [isCreating, setIsCreating] = useState(false);
  const [result, setResult] = useState<TokenCreationResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!connected || !wallet?.adapter) {
      setError('Please connect your wallet first');
      return;
    }

    setIsCreating(true);
    setError(null);
    setResult(null);

    try {
      const tokenService = new TokenCreationService(wallet.adapter);
      
      const metadata: TokenMetadata = {
        name: formData.name,
        symbol: formData.symbol,
        description: formData.description,
        image: formData.image,
        ...(formData.creatorName && {
          creator: {
            name: formData.creatorName,
            site: formData.creatorSite,
          }
        }),
        external_url: formData.externalUrl,
        twitter: formData.twitter,
        telegram: formData.telegram,
        discord: formData.discord,
      };

      const params: TokenCreationParams = {
        metadata,
        decimals: formData.decimals,
        initialSupply: BigInt(Number(formData.initialSupply) * Math.pow(10, formData.decimals)),
        sellerFeeBasisPoints: formData.sellerFeeBasisPoints,
        revokeAuthorities: formData.revokeAuthorities,
      };

      const creationResult = await tokenService.createToken(params);
      setResult(creationResult);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setIsCreating(false);
    }
  };

  if (!connected) {
    return (
      <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg">
        <h2 className="text-2xl font-bold text-gray-800 mb-4">Create Solana Token</h2>
        <p className="text-gray-600 mb-4">Please connect your Phantom wallet to create a token.</p>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold text-gray-800 mb-6">Create Solana Token</h2>
      
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Token Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
              Token Name *
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="My Token"
            />
          </div>
          
          <div>
            <label htmlFor="symbol" className="block text-sm font-medium text-gray-700 mb-2">
              Token Symbol *
            </label>
            <input
              type="text"
              id="symbol"
              name="symbol"
              value={formData.symbol}
              onChange={handleInputChange}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="MTK"
              maxLength={10}
            />
          </div>
        </div>

        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
            Description *
          </label>
          <textarea
            id="description"
            name="description"
            value={formData.description}
            onChange={handleInputChange}
            required
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Describe your token..."
          />
        </div>

        <div>
          <label htmlFor="image" className="block text-sm font-medium text-gray-700 mb-2">
            Image URL *
          </label>
          <input
            type="url"
            id="image"
            name="image"
            value={formData.image}
            onChange={handleInputChange}
            required
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="https://example.com/token-image.png"
          />
        </div>

        {/* Token Configuration */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label htmlFor="decimals" className="block text-sm font-medium text-gray-700 mb-2">
              Decimals
            </label>
            <input
              type="number"
              id="decimals"
              name="decimals"
              value={formData.decimals}
              onChange={handleInputChange}
              min="0"
              max="9"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div>
            <label htmlFor="initialSupply" className="block text-sm font-medium text-gray-700 mb-2">
              Initial Supply
            </label>
            <input
              type="number"
              id="initialSupply"
              name="initialSupply"
              value={formData.initialSupply}
              onChange={handleInputChange}
              min="1"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div>
            <label htmlFor="sellerFeeBasisPoints" className="block text-sm font-medium text-gray-700 mb-2">
              Royalty (basis points)
            </label>
            <input
              type="number"
              id="sellerFeeBasisPoints"
              name="sellerFeeBasisPoints"
              value={formData.sellerFeeBasisPoints}
              onChange={handleInputChange}
              min="0"
              max="10000"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        {/* Optional Fields */}
        <div className="border-t pt-6">
          <h3 className="text-lg font-medium text-gray-800 mb-4">Optional Information</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="creatorName" className="block text-sm font-medium text-gray-700 mb-2">
                Creator Name
              </label>
              <input
                type="text"
                id="creatorName"
                name="creatorName"
                value={formData.creatorName}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Creator Name"
              />
            </div>
            
            <div>
              <label htmlFor="creatorSite" className="block text-sm font-medium text-gray-700 mb-2">
                Creator Website
              </label>
              <input
                type="url"
                id="creatorSite"
                name="creatorSite"
                value={formData.creatorSite}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="https://creator-website.com"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <div>
              <label htmlFor="twitter" className="block text-sm font-medium text-gray-700 mb-2">
                Twitter
              </label>
              <input
                type="text"
                id="twitter"
                name="twitter"
                value={formData.twitter}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="@username"
              />
            </div>
            
            <div>
              <label htmlFor="telegram" className="block text-sm font-medium text-gray-700 mb-2">
                Telegram
              </label>
              <input
                type="text"
                id="telegram"
                name="telegram"
                value={formData.telegram}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="@username"
              />
            </div>
          </div>
        </div>

        {/* Settings */}
        <div className="border-t pt-6">
          <div className="flex items-center">
            <input
              type="checkbox"
              id="revokeAuthorities"
              name="revokeAuthorities"
              checked={formData.revokeAuthorities}
              onChange={handleInputChange}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="revokeAuthorities" className="ml-2 block text-sm text-gray-700">
              Revoke mint and freeze authorities (recommended for security)
            </label>
          </div>
        </div>

        {/* Submit Button */}
        <button
          type="submit"
          disabled={isCreating}
          className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isCreating ? 'Creating Token...' : 'Create Token'}
        </button>
      </form>

      {/* Status Display */}
      <TransactionStatus
        isCreating={isCreating}
        result={result}
        error={error}
      />
    </div>
  );
};
