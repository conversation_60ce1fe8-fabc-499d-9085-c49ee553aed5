
import { WalletConnectionProvider } from './components/WalletConnectionProvider';
import { WalletConnection } from './components/WalletConnection';
import { TokenCreationForm } from './components/TokenCreationForm';

function App() {
  return (
    <WalletConnectionProvider>
      <div className="min-h-screen bg-gray-100">
        <WalletConnection />
        <main className="container mx-auto py-8 px-4">
          <TokenCreationForm />
        </main>
      </div>
    </WalletConnectionProvider>
  );
}

export default App;
